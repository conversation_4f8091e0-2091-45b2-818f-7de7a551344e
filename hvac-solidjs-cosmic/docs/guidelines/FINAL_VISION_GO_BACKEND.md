# 🛠️ FINAL VISION: GO BACKEND FOR HVAC CRM
## *Building a Robust, Scalable, and Secure Backend with <PERSON> and Kratos*

---

## 🌟 **VISION OVERVIEW**

### **Mission Statement**
```
"To architect a backend for HVAC CRM that is unbreakable, infinitely scalable, 
and intelligently integrated, supporting cosmic-level performance and security 
for a 2137% complete enterprise solution."
```

### **Core Objectives**
1. **Rock-Solid Reliability** - Achieve 99.99% uptime with zero-downtime deployments.
2. **Infinite Scalability** - Handle 100,000+ concurrent users and petabytes of data.
3. **Security Fortification** - Implement military-grade encryption and access control.
4. **Intelligent Automation** - Embed AI-driven workflows and analytics.
5. **Seamless Integration** - Provide robust APIs for SolidJS frontend and third-party systems.

---

## ⚙️ **GO & KRATOS ARCHITECTURE**

### **Why Go and Kratos?**
- **Performance** - Go's compiled nature ensures low-latency API responses.
- **Concurrency** - Goroutines enable handling thousands of requests simultaneously.
- **Kratos Framework** - Provides microservice-ready architecture with built-in tools for scalability.
- **Reliability** - Strong typing and error handling minimize runtime errors.
- **Ecosystem** - Rich libraries for database, caching, and AI integrations.

### **Backend Structure**
```
GoBackend-Kratos/
├── api/                       # API definitions (gRPC, REST)
│   ├── hvac/                  # HVAC-specific service definitions
│   └── ai/                    # AI service endpoints
├── internal/                  # Core implementation
│   ├── biz/                   # Business logic layer
│   ├── data/                  # Data access layer (DB, cache)
│   ├── entity/                # Domain models
│   ├── storage/               # File and object storage
│   ├── extraction/            # Document processing (PDF, XLSX)
│   └── ai/                    # AI model integrations (Bielik V3, Gemma)
├── cmd/                       # Entry points for services
│   ├── server/                # Main API server
│   ├── dual-email-processor/  # Email processing service
│   └── octopus/               # Workflow orchestration
├── configs/                   # Configuration files (YAML)
├── migrations/                # Database schema migrations
└── logs/                      # Runtime logs
```

### **Microservice Architecture**
- **Service Decomposition** - Separate services for user management, HVAC operations, AI processing, and communications.
- **gRPC Communication** - High-performance internal communication between services.
- **RESTful APIs** - Expose user-friendly endpoints for frontend consumption via tRPC.
- **Event-Driven** - Use message queues (RabbitMQ/Kafka) for asynchronous processing.

---

## 🗄️ **DATA MANAGEMENT**

### **Database Strategy**
- **PostgreSQL Core** - Primary relational database for structured data with 20+ models.
- **Redis Cache** - In-memory caching for hot data and session management.
- **TimescaleDB Extension** - For time-series data like equipment metrics and financial trends.
- **Schema Design** - Normalized for integrity, with denormalized views for performance.

### **Key Models & Relationships**
```go
// Core HVAC entities (simplified)
type Customer struct {
    ID          string
    Name        string
    Email       string
    Phone       string
    Address     Address
    ServiceOrders []ServiceOrder
    Equipment   []Equipment
    CreatedAt   time.Time
    UpdatedAt   time.Time
}

type ServiceOrder struct {
    ID          string
    CustomerID  string
    EquipmentID string
    Status      string
    ScheduledAt time.Time
    CompletedAt time.Time
    Technician  Technician
    Notes       string
}

type Equipment struct {
    ID           string
    CustomerID   string
    Model        string
    SerialNumber string
    InstallDate  time.Time
    MaintenanceHistory []MaintenanceRecord
}
```

### **Data Flow & Caching**
- **Read Path** - Check Redis cache first, fall back to PostgreSQL if cache miss.
- **Write Path** - Write-through to PostgreSQL, async update to Redis.
- **Event Sourcing** - Log critical state changes for audit and recovery.

---

## 🔒 **SECURITY ARCHITECTURE**

### **Authentication & Authorization**
- **OAuth 2.0 with Kratos** - Self-hosted identity management for secure authentication.
- **Multi-Factor Authentication (MFA)** - Enforce 2FA for all admin accounts.
- **Role-Based Access Control (RBAC)** - Granular permissions per user role.
- **Session Management** - Secure JWT tokens with short expiration and refresh mechanism.

### **Data Protection**
- **Encryption at Rest** - AES-256 for database and file storage.
- **Encryption in Transit** - TLS 1.3 for all network communications.
- **GDPR Compliance** - Data anonymization and deletion workflows for user privacy.

### **API Security**
- **Rate Limiting** - Prevent abuse with per-user request limits.
- **Input Validation** - Strict validation and sanitization of all inputs.
- **CSP & CORS** - Tight content security policies and cross-origin restrictions.

---

## 🤖 **AI & AUTOMATION**

### **AI Service Integration**
- **Bielik V3 & Gemma Models** - Deployed for predictive maintenance and customer sentiment analysis.
- **Email Intelligence** - Automated email processing with NLP for intent detection.
- **Workflow Automation** - AI-driven task assignment and scheduling optimization.

### **AI Data Pipeline**
```go
// AI processing flow
func ProcessEmailForInsights(email Email) (Insights, error) {
    // Extract text and metadata
    content := extractEmailContent(email)
    
    // Vectorize for AI processing
    vector := vectorizeContent(content)
    
    // Analyze with Gemma model
    sentiment := analyzeSentiment(vector)
    intent := detectIntent(vector)
    priority := calculatePriority(sentiment, intent)
    
    // Store results
    storeAnalysisResults(email.ID, sentiment, intent, priority)
    
    return Insights{
        Sentiment: sentiment,
        Intent:    intent,
        Priority:  priority,
    }, nil
}
```

---

## 📈 **SCALABILITY & PERFORMANCE**

### **Horizontal Scaling**
- **Containerization** - Dockerized services for easy scaling.
- **Kubernetes Orchestration** - Automated deployment and load balancing.
- **Service Discovery** - Dynamic routing with Consul or similar.

### **Performance Goals**
- **API Latency** - ≤ 50ms for 99% of requests under load.
- **Throughput** - Handle 10,000 requests per second per service instance.
- **Database Queries** - Optimized with indexes for sub-10ms reads.

### **Caching Strategy**
- **Multi-Level Cache** - Redis for hot data, in-memory for frequently accessed objects.
- **Cache Invalidation** - Smart invalidation on data updates with event triggers.
- **CDN Integration** - Static assets and reports served via Cloudflare or similar.

---

## 🛠️ **IMPLEMENTATION PLAN**

### **Phase 1: Core Infrastructure (Week 1-2)**
- **Setup** - Go environment with Kratos framework, Docker configurations.
- **Database** - PostgreSQL with initial schema and migrations.
- **Authentication** - Kratos identity service with OAuth 2.0.

### **Phase 2: Core Services (Week 3-5)**
- **Customer Service** - CRUD operations for customer management.
- **Order Service** - Service order lifecycle management.
- **Equipment Service** - Asset tracking and maintenance history.
- **API Gateway** - Unified entry point with tRPC for frontend.

### **Phase 3: Advanced Services (Week 6-7)**
- **Financial Service** - Billing, invoicing, and reporting.
- **Communication Service** - Email, SMS, and notification hub.
- **AI Service** - Deploy models for analytics and automation.
- **Workflow Service** - Orchestrate complex business processes.

### **Phase 4: Optimization & Deployment (Week 8)**
- **Performance Tuning** - Stress test and optimize bottlenecks.
- **Security Audit** - Penetration testing and vulnerability scanning.
- **Monitoring** - Implement Prometheus and Grafana for observability.
- **Production Deployment** - Roll out with blue-green deployment strategy.

---

## 🔗 **INTEGRATION WITH SOLIDJS FRONTEND**

### **API Contract**
- **tRPC Endpoints** - Type-safe API contracts auto-generated for frontend.
- **Real-Time Updates** - WebSocket channels for live data sync.
- **Error Handling** - Standardized error codes and messages for UI feedback.

### **Data Synchronization**
```go
// Real-time update handler
func HandleRealTimeUpdates(w http.ResponseWriter, r *http.Request) {
    conn, err := upgrader.Upgrade(w, r, nil)
    if err != nil {
        log.Error("WebSocket upgrade failed", err)
        return
    }
    defer conn.Close()
    
    client := registerClient(conn)
    defer unregisterClient(client)
    
    // Subscribe to updates
    updates := subscribeToChanges(client.UserID)
    defer unsubscribeFromChanges(client.UserID)
    
    for update := range updates {
        err := conn.WriteJSON(update)
        if err != nil {
            log.Error("WebSocket write failed", err)
            break
        }
    }
}
```

---

## 📊 **SUCCESS METRICS**

### **Performance Metrics**
- **API Response Time** - ≤ 50ms for 99% of requests.
- **Uptime** - 99.99% availability with no unplanned downtime.
- **Scalability** - Support 100,000 concurrent users with linear scaling.

### **Security Metrics**
- **Vulnerabilities** - Zero critical or high-severity issues in audits.
- **Authentication** - 100% of endpoints protected with proper auth.
- **Data Breaches** - Zero incidents of unauthorized data access.

### **Reliability Metrics**
- **Error Rate** - ≤ 0.1% of requests result in server errors.
- **Recovery Time** - Mean Time to Recovery (MTTR) under 5 minutes.
- **Backup Frequency** - Full backups daily, incremental every hour.

---

## 🌌 **FUTURE VISION**

### **Advanced Capabilities**
- **Blockchain Integration** - Immutable audit trails for critical transactions.
- **IoT Integration** - Real-time equipment monitoring with sensor data.
- **Global Distribution** - Multi-region deployment for low-latency access worldwide.

### **Scalability Goals**
- **User Base** - Scale to 1M+ active users with no performance impact.
- **Data Volume** - Handle petabytes of historical data with efficient archiving.
- **Service Expansion** - Modular design for adding new HVAC verticals (e.g., industrial, commercial).

---

*"With Go and Kratos, we are forging the backbone of the HVAC CRM universe—a backend so robust and intelligent that it anticipates needs before they arise, ensuring every interaction is seamless, secure, and instantaneous."*

---

## 🚀 **COSMIC-LEVEL BACKEND FEATURES**

### **Enterprise-Grade Microservices**
```go
// Service registry with health monitoring
type ServiceRegistry struct {
    services map[string]*ServiceInfo
    health   *HealthChecker
    logger   log.Logger
    mutex    sync.RWMutex
}

type ServiceInfo struct {
    Name        string            `json:"name"`
    Version     string            `json:"version"`
    Endpoint    string            `json:"endpoint"`
    Health      HealthStatus      `json:"health"`
    Metadata    map[string]string `json:"metadata"`
    LastSeen    time.Time         `json:"last_seen"`
    Metrics     ServiceMetrics    `json:"metrics"`
}

func (sr *ServiceRegistry) RegisterService(ctx context.Context, info *ServiceInfo) error {
    sr.mutex.Lock()
    defer sr.mutex.Unlock()

    info.LastSeen = time.Now()
    sr.services[info.Name] = info

    // Start health monitoring
    go sr.monitorServiceHealth(ctx, info)

    sr.logger.Info("Service registered",
        "name", info.Name,
        "version", info.Version,
        "endpoint", info.Endpoint)

    return nil
}

func (sr *ServiceRegistry) DiscoverService(name string) (*ServiceInfo, error) {
    sr.mutex.RLock()
    defer sr.mutex.RUnlock()

    service, exists := sr.services[name]
    if !exists {
        return nil, ErrServiceNotFound
    }

    if service.Health != HealthStatusHealthy {
        return nil, ErrServiceUnhealthy
    }

    return service, nil
}
```

### **AI-Powered Business Intelligence**
```go
// Advanced analytics engine with machine learning
type BusinessIntelligenceService struct {
    bielikClient    *bielik.Client
    gemmaClient     *gemma.Client
    dataWarehouse   *warehouse.Client
    predictionCache *cache.PredictionCache
    logger          log.Logger
}

func (bi *BusinessIntelligenceService) GeneratePredictiveInsights(ctx context.Context, req *InsightsRequest) (*BusinessInsights, error) {
    // Gather multi-dimensional data
    customerData, err := bi.dataWarehouse.GetCustomerMetrics(ctx, req.TimeRange)
    if err != nil {
        return nil, err
    }

    serviceData, err := bi.dataWarehouse.GetServiceMetrics(ctx, req.TimeRange)
    if err != nil {
        return nil, err
    }

    financialData, err := bi.dataWarehouse.GetFinancialMetrics(ctx, req.TimeRange)
    if err != nil {
        return nil, err
    }

    // AI-powered analysis
    insights := &BusinessInsights{}

    // Customer churn prediction
    churnPrediction, err := bi.bielikClient.PredictChurn(ctx, &bielik.ChurnRequest{
        CustomerData: customerData,
        ServiceData:  serviceData,
        TimeHorizon:  req.PredictionHorizon,
    })
    if err != nil {
        return nil, err
    }
    insights.ChurnPrediction = churnPrediction

    // Revenue forecasting
    revenueForecast, err := bi.gemmaClient.ForecastRevenue(ctx, &gemma.ForecastRequest{
        HistoricalData: financialData,
        SeasonalFactors: req.SeasonalFactors,
        ExternalFactors: req.ExternalFactors,
    })
    if err != nil {
        return nil, err
    }
    insights.RevenueForecast = revenueForecast

    // Equipment failure prediction
    equipmentPredictions, err := bi.predictEquipmentFailures(ctx, serviceData)
    if err != nil {
        return nil, err
    }
    insights.EquipmentPredictions = equipmentPredictions

    // Cache results for performance
    bi.predictionCache.Set(req.CacheKey(), insights, time.Hour)

    return insights, nil
}

func (bi *BusinessIntelligenceService) OptimizeServiceRoutes(ctx context.Context, orders []*ServiceOrder) (*RouteOptimization, error) {
    // AI-powered route optimization
    optimization, err := bi.gemmaClient.OptimizeRoutes(ctx, &gemma.RouteRequest{
        ServiceOrders: orders,
        Technicians:   bi.getAvailableTechnicians(ctx),
        Constraints:   bi.getRouteConstraints(),
        Objectives:    []string{"minimize_travel_time", "maximize_efficiency", "balance_workload"},
    })

    if err != nil {
        return nil, err
    }

    return &RouteOptimization{
        OptimizedRoutes:   optimization.Routes,
        EstimatedSavings:  optimization.TimeSavings,
        EfficiencyGain:    optimization.EfficiencyImprovement,
        RecommendedActions: optimization.Recommendations,
    }, nil
}
```

### **Real-Time Event Processing**
```go
// Event-driven architecture with real-time processing
type EventProcessor struct {
    eventBus    *eventbus.Bus
    handlers    map[string][]EventHandler
    metrics     *metrics.Collector
    logger      log.Logger
}

type Event struct {
    ID          string                 `json:"id"`
    Type        string                 `json:"type"`
    Source      string                 `json:"source"`
    Data        map[string]interface{} `json:"data"`
    Timestamp   time.Time              `json:"timestamp"`
    Metadata    map[string]string      `json:"metadata"`
}

func (ep *EventProcessor) PublishEvent(ctx context.Context, event *Event) error {
    event.ID = generateEventID()
    event.Timestamp = time.Now()

    // Validate event
    if err := ep.validateEvent(event); err != nil {
        return err
    }

    // Publish to event bus
    if err := ep.eventBus.Publish(ctx, event); err != nil {
        return err
    }

    // Update metrics
    ep.metrics.IncrementCounter("events_published", map[string]string{
        "type":   event.Type,
        "source": event.Source,
    })

    ep.logger.Info("Event published",
        "id", event.ID,
        "type", event.Type,
        "source", event.Source)

    return nil
}

func (ep *EventProcessor) HandleServiceOrderCreated(ctx context.Context, event *Event) error {
    orderData := event.Data["service_order"].(map[string]interface{})

    // Parallel processing of multiple workflows
    var wg sync.WaitGroup
    errChan := make(chan error, 4)

    // Auto-assign technician
    wg.Add(1)
    go func() {
        defer wg.Done()
        if err := ep.autoAssignTechnician(ctx, orderData); err != nil {
            errChan <- err
        }
    }()

    // Send customer notification
    wg.Add(1)
    go func() {
        defer wg.Done()
        if err := ep.sendCustomerNotification(ctx, orderData); err != nil {
            errChan <- err
        }
    }()

    // Update inventory
    wg.Add(1)
    go func() {
        defer wg.Done()
        if err := ep.updateInventoryReservation(ctx, orderData); err != nil {
            errChan <- err
        }
    }()

    // Generate predictive insights
    wg.Add(1)
    go func() {
        defer wg.Done()
        if err := ep.generateServiceInsights(ctx, orderData); err != nil {
            errChan <- err
        }
    }()

    wg.Wait()
    close(errChan)

    // Collect any errors
    var errors []error
    for err := range errChan {
        if err != nil {
            errors = append(errors, err)
        }
    }

    if len(errors) > 0 {
        return fmt.Errorf("workflow errors: %v", errors)
    }

    return nil
}
```

### **Advanced Caching & Performance**
```go
// Multi-tier caching system with intelligent invalidation
type CacheManager struct {
    l1Cache    *cache.InMemoryCache    // Hot data (1-10 seconds TTL)
    l2Cache    *redis.Client           // Warm data (1-60 minutes TTL)
    l3Cache    *cdn.Client             // Cold data (1-24 hours TTL)
    invalidator *cache.Invalidator
    metrics    *metrics.Collector
    logger     log.Logger
}

func (cm *CacheManager) Get(ctx context.Context, key string) (interface{}, error) {
    start := time.Now()
    defer func() {
        cm.metrics.RecordDuration("cache_get_duration", time.Since(start), map[string]string{
            "key": key,
        })
    }()

    // L1 Cache (In-Memory)
    if value, found := cm.l1Cache.Get(key); found {
        cm.metrics.IncrementCounter("cache_hits", map[string]string{"level": "l1"})
        return value, nil
    }

    // L2 Cache (Redis)
    if value, err := cm.l2Cache.Get(ctx, key).Result(); err == nil {
        cm.metrics.IncrementCounter("cache_hits", map[string]string{"level": "l2"})

        // Promote to L1
        cm.l1Cache.Set(key, value, time.Minute)
        return value, nil
    }

    // L3 Cache (CDN)
    if value, err := cm.l3Cache.Get(ctx, key); err == nil {
        cm.metrics.IncrementCounter("cache_hits", map[string]string{"level": "l3"})

        // Promote to L2 and L1
        cm.l2Cache.Set(ctx, key, value, time.Hour)
        cm.l1Cache.Set(key, value, time.Minute)
        return value, nil
    }

    cm.metrics.IncrementCounter("cache_misses", nil)
    return nil, cache.ErrNotFound
}

func (cm *CacheManager) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
    // Intelligent tier placement based on access patterns
    accessPattern := cm.analyzeAccessPattern(key)

    switch accessPattern.Tier {
    case cache.HotTier:
        cm.l1Cache.Set(key, value, ttl)
        cm.l2Cache.Set(ctx, key, value, ttl*10)
    case cache.WarmTier:
        cm.l2Cache.Set(ctx, key, value, ttl)
    case cache.ColdTier:
        cm.l3Cache.Set(ctx, key, value, ttl)
    }

    return nil
}

func (cm *CacheManager) InvalidatePattern(ctx context.Context, pattern string) error {
    // Smart invalidation across all tiers
    return cm.invalidator.InvalidatePattern(ctx, pattern)
}
```

---

## 🔐 **ENTERPRISE SECURITY FRAMEWORK**

### **Zero-Trust Architecture**
```go
// Comprehensive security service
type SecurityFramework struct {
    authService     *auth.Service
    rbacService     *rbac.Service
    auditService    *audit.Service
    encryptionService *encryption.Service
    threatDetection *threat.DetectionService
    complianceService *compliance.Service
}

func (sf *SecurityFramework) AuthorizeRequest(ctx context.Context, req *AuthRequest) (*AuthResult, error) {
    // Multi-factor authentication
    authResult, err := sf.authService.Authenticate(ctx, req.Credentials)
    if err != nil {
        sf.auditService.LogFailedAuth(ctx, req)
        return nil, err
    }

    // Role-based access control
    permissions, err := sf.rbacService.GetPermissions(ctx, authResult.UserID, req.Resource)
    if err != nil {
        return nil, err
    }

    // Threat detection
    threatScore, err := sf.threatDetection.AnalyzeRequest(ctx, req)
    if err != nil {
        return nil, err
    }

    if threatScore > sf.threatDetection.GetThreshold() {
        sf.auditService.LogThreatDetected(ctx, req, threatScore)
        return nil, ErrThreatDetected
    }

    // Compliance validation
    if err := sf.complianceService.ValidateAccess(ctx, authResult.UserID, req.Resource); err != nil {
        return nil, err
    }

    // Audit successful access
    sf.auditService.LogSuccessfulAccess(ctx, authResult.UserID, req.Resource)

    return &AuthResult{
        UserID:      authResult.UserID,
        Permissions: permissions,
        SessionID:   authResult.SessionID,
        ExpiresAt:   authResult.ExpiresAt,
    }, nil
}

func (sf *SecurityFramework) EncryptSensitiveData(ctx context.Context, data []byte, classification DataClassification) ([]byte, error) {
    switch classification {
    case DataClassificationPublic:
        return data, nil
    case DataClassificationInternal:
        return sf.encryptionService.EncryptAES256(data)
    case DataClassificationConfidential:
        return sf.encryptionService.EncryptAES256GCM(data)
    case DataClassificationRestricted:
        return sf.encryptionService.EncryptChaCha20Poly1305(data)
    default:
        return nil, ErrInvalidClassification
    }
}
```

### **Compliance & Audit Framework**
```go
// GDPR, SOC2, and industry compliance
type ComplianceService struct {
    auditLogger     *audit.Logger
    dataProcessor   *gdpr.DataProcessor
    retentionPolicy *retention.PolicyEngine
    anonymizer      *data.Anonymizer
}

func (cs *ComplianceService) ProcessDataSubjectRequest(ctx context.Context, req *DataSubjectRequest) error {
    switch req.Type {
    case DataSubjectRequestTypeAccess:
        return cs.handleDataAccess(ctx, req)
    case DataSubjectRequestTypeRectification:
        return cs.handleDataRectification(ctx, req)
    case DataSubjectRequestTypeErasure:
        return cs.handleDataErasure(ctx, req)
    case DataSubjectRequestTypePortability:
        return cs.handleDataPortability(ctx, req)
    default:
        return ErrInvalidRequestType
    }
}

func (cs *ComplianceService) handleDataErasure(ctx context.Context, req *DataSubjectRequest) error {
    // Find all data related to the subject
    dataLocations, err := cs.findPersonalData(ctx, req.SubjectID)
    if err != nil {
        return err
    }

    // Apply retention policies
    for _, location := range dataLocations {
        policy, err := cs.retentionPolicy.GetPolicy(location.DataType)
        if err != nil {
            return err
        }

        if policy.CanErase(location.CreatedAt) {
            if err := cs.eraseData(ctx, location); err != nil {
                return err
            }
        } else {
            // Anonymize instead of delete
            if err := cs.anonymizer.AnonymizeData(ctx, location); err != nil {
                return err
            }
        }
    }

    // Audit the erasure
    cs.auditLogger.LogDataErasure(ctx, req.SubjectID, dataLocations)

    return nil
}
```

---

## 📊 **OBSERVABILITY & MONITORING**

### **Comprehensive Monitoring Stack**
```go
// Observability service with metrics, tracing, and logging
type ObservabilityService struct {
    metricsCollector *prometheus.Collector
    tracer          *jaeger.Tracer
    logger          *zap.Logger
    alertManager    *alert.Manager
}

func (obs *ObservabilityService) TraceRequest(ctx context.Context, operationName string) (context.Context, func()) {
    span, ctx := obs.tracer.StartSpanFromContext(ctx, operationName)

    startTime := time.Now()

    return ctx, func() {
        duration := time.Since(startTime)

        // Record metrics
        obs.metricsCollector.RecordDuration("operation_duration", duration, map[string]string{
            "operation": operationName,
            "status":    "success",
        })

        // Add span tags
        span.SetTag("duration_ms", duration.Milliseconds())
        span.Finish()
    }
}

func (obs *ObservabilityService) RecordBusinessMetric(metric string, value float64, tags map[string]string) {
    obs.metricsCollector.RecordGauge(metric, value, tags)

    // Check for alerts
    if alert := obs.alertManager.CheckThreshold(metric, value); alert != nil {
        obs.triggerAlert(alert)
    }
}

func (obs *ObservabilityService) LogStructured(level zapcore.Level, message string, fields ...zap.Field) {
    obs.logger.Log(level, message, fields...)

    // Send critical logs to alert manager
    if level >= zapcore.ErrorLevel {
        obs.alertManager.ProcessLogAlert(message, fields)
    }
}
```

**Next:** [Final Vision: SolidJS Frontend](./FINAL_VISION_SOLIDJS.md)
